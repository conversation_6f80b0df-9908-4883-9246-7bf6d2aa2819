/* 全局样式 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background-color: #f5f5f5;
}

#app {
  height: 100%;
}

/* 清除默认样式 */
ul, ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

a {
  color: inherit;
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 布局样式 */
.layout {
  display: flex;
  height: 100vh;
}

.layout-sidebar {
  width: 200px;
  background: #001529;
  transition: width 0.3s;
}

.layout-sidebar.collapsed {
  width: 64px;
}

.layout-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.layout-header {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.layout-content {
  flex: 1;
  padding: 20px;
  overflow: auto;
  background: #f5f5f5;
}

/* 卡片样式 */
.card {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  border: 1px solid #e8e8e8;
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  font-weight: 500;
}

.card-body {
  padding: 20px;
}

/* 表格样式 */
.table-container {
  background: #fff;
  border-radius: 6px;
  overflow: hidden;
}

.table-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-weight: 500;
  font-size: 16px;
}

.table-actions {
  display: flex;
  gap: 12px;
}

/* 表单样式 */
.form-container {
  background: #fff;
  border-radius: 6px;
  padding: 20px;
}

.form-actions {
  text-align: center;
  margin-top: 24px;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: 8px;
}

.mb-2 {
  margin-bottom: 16px;
}

.mb-3 {
  margin-bottom: 24px;
}

.mt-0 {
  margin-top: 0;
}

.mt-1 {
  margin-top: 8px;
}

.mt-2 {
  margin-top: 16px;
}

.mt-3 {
  margin-top: 24px;
}

.ml-1 {
  margin-left: 8px;
}

.ml-2 {
  margin-left: 16px;
}

.mr-1 {
  margin-right: 8px;
}

.mr-2 {
  margin-right: 16px;
}

/* 状态样式 */
.status-active {
  color: #52c41a;
}

.status-inactive {
  color: #ff4d4f;
}

.status-pending {
  color: #faad14;
}

/* 响应式 */
@media (max-width: 768px) {
  .layout-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s;
  }
  
  .layout-sidebar.show {
    transform: translateX(0);
  }
  
  .layout-main {
    margin-left: 0;
  }
  
  .layout-content {
    padding: 12px;
  }
  
  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .table-actions {
    justify-content: center;
  }
}

/* 动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

/* NProgress样式覆盖 */
#nprogress .bar {
  background: #409eff !important;
  height: 3px !important;
}

#nprogress .peg {
  box-shadow: 0 0 10px #409eff, 0 0 5px #409eff !important;
}

/* Element Plus样式覆盖 */
.el-menu {
  border-right: none;
}

.el-menu--dark {
  background-color: #001529;
}

.el-menu--dark .el-menu-item {
  color: rgba(255, 255, 255, 0.65);
}

.el-menu--dark .el-menu-item:hover {
  background-color: #1890ff;
  color: #fff;
}

.el-menu--dark .el-menu-item.is-active {
  background-color: #1890ff;
  color: #fff;
}

.el-menu--dark .el-sub-menu__title {
  color: rgba(255, 255, 255, 0.65);
}

.el-menu--dark .el-sub-menu__title:hover {
  background-color: #1890ff;
  color: #fff;
}
