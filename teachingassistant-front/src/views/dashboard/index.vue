<template>
  <div class="dashboard">
    <!-- 欢迎信息 -->
    <div class="welcome-card">
      <div class="welcome-content">
        <h2 class="welcome-title">
          欢迎回来，{{ userStore.userName }}！
        </h2>
        <p class="welcome-subtitle">
          {{ getRoleText(userStore.userInfo?.role) }}
          <span v-if="userStore.schoolName"> - {{ userStore.schoolName }}</span>
        </p>
        <p class="welcome-time">{{ getCurrentTime() }}</p>
      </div>
      <div class="welcome-avatar">
        <el-avatar :size="80">
          <el-icon><User /></el-icon>
        </el-avatar>
      </div>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card" v-for="stat in stats" :key="stat.title">
        <div class="stat-icon" :style="{ backgroundColor: stat.color }">
          <el-icon :size="24">
            <component :is="stat.icon" />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-title">{{ stat.title }}</div>
        </div>
      </div>
    </div>
    
    <!-- 快捷操作 -->
    <div class="quick-actions">
      <h3 class="section-title">快捷操作</h3>
      <div class="actions-grid">
        <div
          class="action-item"
          v-for="action in quickActions"
          :key="action.title"
          @click="handleAction(action)"
        >
          <el-icon :size="32" :color="action.color">
            <component :is="action.icon" />
          </el-icon>
          <span class="action-title">{{ action.title }}</span>
        </div>
      </div>
    </div>
    
    <!-- 最近活动 -->
    <div class="recent-activities">
      <h3 class="section-title">最近活动</h3>
      <div class="activity-list">
        <div class="activity-item" v-for="activity in activities" :key="activity.id">
          <div class="activity-icon">
            <el-icon :color="activity.color">
              <component :is="activity.icon" />
            </el-icon>
          </div>
          <div class="activity-content">
            <div class="activity-title">{{ activity.title }}</div>
            <div class="activity-time">{{ activity.time }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import dayjs from 'dayjs'

const router = useRouter()
const userStore = useUserStore()

// 统计数据
const stats = ref([
  { title: '今日课程', value: '8', icon: 'Calendar', color: '#409eff' },
  { title: '本月课时', value: '156', icon: 'Clock', color: '#67c23a' },
  { title: '学生总数', value: '45', icon: 'UserFilled', color: '#e6a23c' },
  { title: '待处理', value: '3', icon: 'Bell', color: '#f56c6c' }
])

// 快捷操作
const quickActions = computed(() => {
  const role = userStore.userInfo?.role
  const actions = []
  
  if (role === 'super_admin') {
    actions.push(
      { title: '学校管理', icon: 'School', color: '#409eff', path: '/admin/schools' },
      { title: '用户管理', icon: 'UserFilled', color: '#67c23a', path: '/admin/users' },
      { title: '系统设置', icon: 'Setting', color: '#e6a23c', path: '/profile/settings' }
    )
  } else if (role === 'principal') {
    actions.push(
      { title: '排课管理', icon: 'Calendar', color: '#409eff', path: '/principal/schedule' },
      { title: '老师管理', icon: 'User', color: '#67c23a', path: '/principal/teachers' },
      { title: '学生管理', icon: 'UserFilled', color: '#e6a23c', path: '/principal/students' },
      { title: '约课中心', icon: 'Clock', color: '#f56c6c', path: '/principal/bookings' }
    )
  } else if (role === 'teacher') {
    actions.push(
      { title: '我的课表', icon: 'Calendar', color: '#409eff', path: '/teacher/schedule' },
      { title: '考勤记录', icon: 'Clock', color: '#67c23a', path: '/teacher/attendance' },
      { title: '我的学生', icon: 'UserFilled', color: '#e6a23c', path: '/teacher/students' },
      { title: '工资查询', icon: 'Money', color: '#f56c6c', path: '/teacher/salary' }
    )
  }
  
  return actions
})

// 最近活动
const activities = ref([
  {
    id: 1,
    title: '张三同学完成了数学课程',
    time: '2小时前',
    icon: 'Check',
    color: '#67c23a'
  },
  {
    id: 2,
    title: '李老师提交了考勤记录',
    time: '4小时前',
    icon: 'Clock',
    color: '#409eff'
  },
  {
    id: 3,
    title: '新增约课请求待处理',
    time: '6小时前',
    icon: 'Bell',
    color: '#e6a23c'
  },
  {
    id: 4,
    title: '王五同学缴费成功',
    time: '1天前',
    icon: 'Money',
    color: '#67c23a'
  }
])

// 获取角色文本
const getRoleText = (role?: string) => {
  const roleMap: Record<string, string> = {
    super_admin: '超级管理员',
    principal: '校长',
    teacher: '老师'
  }
  return roleMap[role || ''] || '用户'
}

// 获取当前时间
const getCurrentTime = () => {
  return dayjs().format('YYYY年MM月DD日 dddd')
}

// 处理快捷操作
const handleAction = (action: any) => {
  if (action.path) {
    router.push(action.path)
  }
}

// 加载数据
const loadData = async () => {
  // TODO: 根据用户角色加载对应的统计数据
  console.log('加载仪表板数据')
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 30px;
  color: white;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.welcome-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0 0 8px 0;
}

.welcome-time {
  font-size: 14px;
  opacity: 0.8;
  margin: 0;
}

.welcome-avatar {
  flex-shrink: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stat-title {
  font-size: 14px;
  color: #666;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.quick-actions {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid #e8e8e8;
}

.action-item:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-title {
  font-size: 14px;
  color: #333;
  text-align: center;
}

.recent-activities {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.activity-item:hover {
  background: #f8f9fa;
}

.activity-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 2px;
}

.activity-time {
  font-size: 12px;
  color: #999;
}

@media (max-width: 768px) {
  .welcome-card {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .action-item {
    padding: 16px;
  }
}
</style>
