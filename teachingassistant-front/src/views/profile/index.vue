<template>
  <div class="profile-container">
    <div class="header">
      <h2>个人信息</h2>
      <el-button type="primary" @click="handleEdit">
        <el-icon><Edit /></el-icon>
        编辑信息
      </el-button>
    </div>

    <div class="profile-content">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="avatar-card">
            <div class="avatar-section">
              <el-avatar :size="120" :src="userInfo.avatar" class="user-avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <div class="avatar-info">
                <h3>{{ userInfo.realName }}</h3>
                <p>{{ getRoleText(userInfo.role) }}</p>
                <el-button type="text" @click="handleChangeAvatar">更换头像</el-button>
              </div>
            </div>
            
            <div class="quick-stats">
              <div class="stat-item">
                <div class="stat-value">{{ userStats.workDays }}</div>
                <div class="stat-label">工作天数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ userStats.totalClasses }}</div>
                <div class="stat-label">授课总数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ userStats.totalStudents }}</div>
                <div class="stat-label">学生总数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="16">
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <span>基本信息</span>
              </div>
            </template>
            
            <el-descriptions :column="2" border>
              <el-descriptions-item label="用户名">{{ userInfo.username }}</el-descriptions-item>
              <el-descriptions-item label="真实姓名">{{ userInfo.realName }}</el-descriptions-item>
              <el-descriptions-item label="角色">{{ getRoleText(userInfo.role) }}</el-descriptions-item>
              <el-descriptions-item label="性别">{{ userInfo.gender === 'M' ? '男' : '女' }}</el-descriptions-item>
              <el-descriptions-item label="手机号">{{ userInfo.phone }}</el-descriptions-item>
              <el-descriptions-item label="邮箱">{{ userInfo.email }}</el-descriptions-item>
              <el-descriptions-item label="入职时间">{{ userInfo.hireDate }}</el-descriptions-item>
              <el-descriptions-item label="所属学校">{{ userInfo.schoolName }}</el-descriptions-item>
              <el-descriptions-item label="主教科目" v-if="userInfo.role === 'teacher'">{{ userInfo.subject }}</el-descriptions-item>
              <el-descriptions-item label="教学经验" v-if="userInfo.role === 'teacher'">{{ userInfo.experience }}年</el-descriptions-item>
              <el-descriptions-item label="最后登录">{{ userInfo.lastLoginAt }}</el-descriptions-item>
              <el-descriptions-item label="账号状态">
                <el-tag :type="userInfo.status === 1 ? 'success' : 'danger'">
                  {{ userInfo.status === 1 ? '正常' : '禁用' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>

          <el-card class="security-card" style="margin-top: 20px;">
            <template #header>
              <div class="card-header">
                <span>安全设置</span>
              </div>
            </template>
            
            <div class="security-items">
              <div class="security-item">
                <div class="security-info">
                  <div class="security-title">登录密码</div>
                  <div class="security-desc">用于登录系统的密码</div>
                </div>
                <el-button type="primary" @click="handleChangePassword">修改密码</el-button>
              </div>
              
              <div class="security-item">
                <div class="security-info">
                  <div class="security-title">手机号码</div>
                  <div class="security-desc">已绑定手机号：{{ userInfo.phone }}</div>
                </div>
                <el-button type="primary" @click="handleChangePhone">更换手机</el-button>
              </div>
              
              <div class="security-item">
                <div class="security-info">
                  <div class="security-title">邮箱地址</div>
                  <div class="security-desc">已绑定邮箱：{{ userInfo.email }}</div>
                </div>
                <el-button type="primary" @click="handleChangeEmail">更换邮箱</el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 编辑信息对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑个人信息"
      width="600px"
      @close="handleEditDialogClose"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="editForm.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="editForm.gender">
            <el-radio label="M">男</el-radio>
            <el-radio label="F">女</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="editForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="主教科目" prop="subject" v-if="userInfo.role === 'teacher'">
          <el-input v-model="editForm.subject" placeholder="请输入主教科目" />
        </el-form-item>
        <el-form-item label="个人简介" prop="bio">
          <el-input
            v-model="editForm.bio"
            type="textarea"
            :rows="4"
            placeholder="请输入个人简介"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveEdit">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="passwordDialogVisible"
      title="修改密码"
      width="500px"
      @close="handlePasswordDialogClose"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="当前密码" prop="oldPassword">
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSavePassword">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 更换头像对话框 -->
    <el-dialog
      v-model="avatarDialogVisible"
      title="更换头像"
      width="400px"
    >
      <div class="avatar-upload">
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
          :before-upload="beforeAvatarUpload"
          :on-success="handleAvatarSuccess"
        >
          <img v-if="newAvatar" :src="newAvatar" class="avatar-preview" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
        <div class="upload-tips">
          <p>支持 JPG、PNG 格式，文件大小不超过 2MB</p>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="avatarDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveAvatar">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, User, Plus } from '@element-plus/icons-vue'

// 响应式数据
const editDialogVisible = ref(false)
const passwordDialogVisible = ref(false)
const avatarDialogVisible = ref(false)
const editFormRef = ref()
const passwordFormRef = ref()
const newAvatar = ref('')

// 用户信息
const userInfo = ref({
  id: 1,
  username: 'teacher001',
  realName: '张老师',
  role: 'teacher',
  gender: 'M',
  phone: '13800138000',
  email: '<EMAIL>',
  avatar: '',
  hireDate: '2023-09-01',
  schoolName: '示例小学',
  subject: '数学',
  experience: 5,
  lastLoginAt: '2024-01-15 10:30:00',
  status: 1,
  bio: '热爱教育事业，专注于数学教学'
})

// 用户统计
const userStats = ref({
  workDays: 120,
  totalClasses: 240,
  totalStudents: 150
})

// 编辑表单
const editForm = reactive({
  realName: '',
  gender: '',
  phone: '',
  email: '',
  subject: '',
  bio: ''
})

// 密码表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const editRules = {
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 方法
const getRoleText = (role) => {
  const roleMap = {
    super_admin: '超级管理员',
    principal: '校长',
    teacher: '教师',
    student: '学生'
  }
  return roleMap[role] || role
}

const fetchUserInfo = async () => {
  try {
    // TODO: 调用API获取用户信息
    // 模拟数据已在上面定义
  } catch (error) {
    ElMessage.error('获取用户信息失败')
  }
}

const fetchUserStats = async () => {
  try {
    // TODO: 调用API获取用户统计数据
    // 模拟数据已在上面定义
  } catch (error) {
    ElMessage.error('获取统计数据失败')
  }
}

const handleEdit = () => {
  Object.assign(editForm, {
    realName: userInfo.value.realName,
    gender: userInfo.value.gender,
    phone: userInfo.value.phone,
    email: userInfo.value.email,
    subject: userInfo.value.subject,
    bio: userInfo.value.bio
  })
  editDialogVisible.value = true
}

const handleSaveEdit = async () => {
  try {
    await editFormRef.value.validate()
    
    // TODO: 调用API保存用户信息
    Object.assign(userInfo.value, editForm)
    
    ElMessage.success('信息更新成功')
    editDialogVisible.value = false
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const handleEditDialogClose = () => {
  editFormRef.value?.resetFields()
}

const handleChangePassword = () => {
  passwordForm.oldPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  passwordDialogVisible.value = true
}

const handleSavePassword = async () => {
  try {
    await passwordFormRef.value.validate()
    
    // TODO: 调用API修改密码
    
    ElMessage.success('密码修改成功')
    passwordDialogVisible.value = false
  } catch (error) {
    ElMessage.error('密码修改失败')
  }
}

const handlePasswordDialogClose = () => {
  passwordFormRef.value?.resetFields()
}

const handleChangePhone = () => {
  ElMessage.info('更换手机号功能待开发')
}

const handleChangeEmail = () => {
  ElMessage.info('更换邮箱功能待开发')
}

const handleChangeAvatar = () => {
  newAvatar.value = userInfo.value.avatar
  avatarDialogVisible.value = true
}

const beforeAvatarUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('头像图片只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('头像图片大小不能超过 2MB!')
    return false
  }
  
  // 预览图片
  const reader = new FileReader()
  reader.onload = (e) => {
    newAvatar.value = e.target.result
  }
  reader.readAsDataURL(file)
  
  return false // 阻止自动上传
}

const handleAvatarSuccess = () => {
  // 处理上传成功
}

const handleSaveAvatar = async () => {
  try {
    // TODO: 调用API保存头像
    userInfo.value.avatar = newAvatar.value
    
    ElMessage.success('头像更新成功')
    avatarDialogVisible.value = false
  } catch (error) {
    ElMessage.error('头像更新失败')
  }
}

// 生命周期
onMounted(() => {
  fetchUserInfo()
  fetchUserStats()
})
</script>

<style scoped>
.profile-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.profile-content {
  margin-top: 20px;
}

.avatar-card {
  text-align: center;
}

.avatar-section {
  padding: 20px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 20px;
}

.user-avatar {
  margin-bottom: 15px;
}

.avatar-info h3 {
  margin: 0 0 5px 0;
  color: #303133;
}

.avatar-info p {
  margin: 0 0 10px 0;
  color: #909399;
}

.quick-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.info-card,
.security-card {
  height: fit-content;
}

.card-header {
  font-weight: bold;
  color: #303133;
}

.security-items {
  space-y: 20px;
}

.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #ebeef5;
}

.security-item:last-child {
  border-bottom: none;
}

.security-info {
  flex: 1;
}

.security-title {
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.security-desc {
  color: #909399;
  font-size: 14px;
}

.avatar-upload {
  text-align: center;
}

.avatar-uploader {
  display: inline-block;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: 0.2s;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}

.avatar-preview {
  width: 120px;
  height: 120px;
  display: block;
}

.upload-tips {
  margin-top: 15px;
}

.upload-tips p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
