import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ showSpinner: false })

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
      hidden: true
    }
  },
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard/index',
    children: [
      {
        path: 'index',
        name: 'DashboardIndex',
        component: () => import('@/views/dashboard/index.vue'),
        meta: {
          title: '工作台',
          icon: 'House'
        }
      }
    ]
  },
  // 超级管理员路由
  {
    path: '/admin',
    name: 'Admin',
    component: () => import('@/layout/index.vue'),
    meta: {
      title: '系统管理',
      icon: 'Setting',
      roles: ['super_admin']
    },
    children: [
      {
        path: 'schools',
        name: 'AdminSchools',
        component: () => import('@/views/admin/schools/index.vue'),
        meta: {
          title: '学校管理',
          icon: 'School'
        }
      },
      {
        path: 'principals',
        name: 'AdminPrincipals',
        component: () => import('@/views/admin/principals/index.vue'),
        meta: {
          title: '校长管理',
          icon: 'User'
        }
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: () => import('@/views/admin/users/index.vue'),
        meta: {
          title: '用户管理',
          icon: 'UserFilled'
        }
      }
    ]
  },
  // 校长路由
  {
    path: '/principal',
    name: 'Principal',
    component: () => import('@/layout/index.vue'),
    meta: {
      title: '校长管理',
      icon: 'Management',
      roles: ['super_admin', 'principal']
    },
    children: [
      {
        path: 'teachers',
        name: 'PrincipalTeachers',
        component: () => import('@/views/principal/teachers/index.vue'),
        meta: {
          title: '老师管理',
          icon: 'User'
        }
      },
      {
        path: 'students',
        name: 'PrincipalStudents',
        component: () => import('@/views/principal/students/index.vue'),
        meta: {
          title: '学生管理',
          icon: 'UserFilled'
        }
      },
      {
        path: 'schedule',
        name: 'PrincipalSchedule',
        component: () => import('@/views/principal/schedule/index.vue'),
        meta: {
          title: '排课管理',
          icon: 'Calendar'
        }
      },
      {
        path: 'classrooms',
        name: 'PrincipalClassrooms',
        component: () => import('@/views/principal/classrooms/index.vue'),
        meta: {
          title: '教室管理',
          icon: 'OfficeBuilding'
        }
      },
      {
        path: 'classes',
        name: 'PrincipalClasses',
        component: () => import('@/views/principal/classes/index.vue'),
        meta: {
          title: '班级管理',
          icon: 'Collection'
        }
      },
      {
        path: 'bookings',
        name: 'PrincipalBookings',
        component: () => import('@/views/principal/bookings/index.vue'),
        meta: {
          title: '约课中心',
          icon: 'Clock'
        }
      },
      {
        path: 'messages',
        name: 'PrincipalMessages',
        component: () => import('@/views/principal/messages/index.vue'),
        meta: {
          title: '消息中心',
          icon: 'Message'
        }
      },
      {
        path: 'finance',
        name: 'PrincipalFinance',
        component: () => import('@/views/principal/finance/index.vue'),
        meta: {
          title: '财务管理',
          icon: 'Money'
        }
      }
    ]
  },
  // 老师路由
  {
    path: '/teacher',
    name: 'Teacher',
    component: () => import('@/layout/index.vue'),
    meta: {
      title: '教学管理',
      icon: 'Reading',
      roles: ['super_admin', 'principal', 'teacher']
    },
    children: [
      {
        path: 'schedule',
        name: 'TeacherSchedule',
        component: () => import('@/views/teacher/schedule/index.vue'),
        meta: {
          title: '我的课表',
          icon: 'Calendar'
        }
      },
      {
        path: 'attendance',
        name: 'TeacherAttendance',
        component: () => import('@/views/teacher/attendance/index.vue'),
        meta: {
          title: '考勤记录',
          icon: 'Clock'
        }
      },
      {
        path: 'students',
        name: 'TeacherStudents',
        component: () => import('@/views/teacher/students/index.vue'),
        meta: {
          title: '我的学生',
          icon: 'UserFilled'
        }
      },
      {
        path: 'salary',
        name: 'TeacherSalary',
        component: () => import('@/views/teacher/salary/index.vue'),
        meta: {
          title: '工资查询',
          icon: 'Money'
        }
      },
      {
        path: 'messages',
        name: 'TeacherMessages',
        component: () => import('@/views/teacher/messages/index.vue'),
        meta: {
          title: '消息中心',
          icon: 'Message'
        }
      }
    ]
  },
  // 个人中心
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/layout/index.vue'),
    meta: {
      title: '个人中心',
      hidden: true
    },
    children: [
      {
        path: 'index',
        name: 'ProfileIndex',
        component: () => import('@/views/profile/index.vue'),
        meta: {
          title: '个人信息',
          icon: 'User'
        }
      },
      {
        path: 'settings',
        name: 'ProfileSettings',
        component: () => import('@/views/profile/settings.vue'),
        meta: {
          title: '系统设置',
          icon: 'Setting'
        }
      },
      {
        path: 'logs',
        name: 'ProfileLogs',
        component: () => import('@/views/profile/logs.vue'),
        meta: {
          title: '操作日志',
          icon: 'Document'
        }
      }
    ]
  },
  // 404页面
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面不存在',
      hidden: true
    }
  },
  // 403页面
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/error/403.vue'),
    meta: {
      title: '权限不足',
      hidden: true
    }
  },
  // 通配符路由，必须放在最后
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior: () => ({ top: 0 })
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  
  const userStore = useUserStore()
  const appStore = useAppStore()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 助教排课系统`
  }
  
  // 白名单路由（不需要登录）
  const whiteList = ['/login', '/404', '/403']
  
  if (whiteList.includes(to.path)) {
    next()
    return
  }
  
  // 检查是否已登录
  if (!userStore.isLoggedIn) {
    next('/login')
    return
  }
  
  // 如果没有用户信息，尝试获取
  if (!userStore.userInfo) {
    try {
      await userStore.getUserInfo()
    } catch (error) {
      console.error('获取用户信息失败:', error)
      userStore.logout()
      next('/login')
      return
    }
  }
  
  // 检查路由权限
  if (to.meta.roles && to.meta.roles.length > 0) {
    const hasPermission = to.meta.roles.includes(userStore.userInfo?.role || '')
    if (!hasPermission) {
      next('/403')
      return
    }
  }
  
  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
