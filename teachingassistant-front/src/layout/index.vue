<template>
  <div class="layout">
    <!-- 侧边栏 -->
    <div 
      class="layout-sidebar"
      :class="{ 
        collapsed: appStore.sidebarCollapsed,
        show: appStore.device === 'mobile' && !appStore.sidebarCollapsed
      }"
    >
      <div class="sidebar-logo">
        <img src="/favicon.ico" alt="Logo" class="logo-img">
        <span v-show="!appStore.sidebarCollapsed" class="logo-text">助教系统</span>
      </div>
      
      <el-menu
        :default-active="activeMenu"
        :collapse="appStore.sidebarCollapsed"
        :unique-opened="true"
        class="sidebar-menu"
        mode="vertical"
        theme="dark"
        router
      >
        <template v-for="route in menuRoutes" :key="route.path">
          <sidebar-item :route="route" />
        </template>
      </el-menu>
    </div>
    
    <!-- 主内容区 -->
    <div class="layout-main">
      <!-- 顶部导航 -->
      <div class="layout-header">
        <div class="header-left">
          <el-button
            type="text"
            size="large"
            @click="appStore.toggleSidebar"
          >
            <el-icon><Expand v-if="appStore.sidebarCollapsed" /><Fold v-else /></el-icon>
          </el-button>
          
          <!-- 面包屑 -->
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item
              v-for="item in breadcrumbs"
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <!-- 用户信息 -->
          <el-dropdown @command="handleCommand">
            <div class="user-info">
              <el-avatar :size="32" :src="userAvatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="user-name">{{ userStore.userName }}</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人中心
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  系统设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      
      <!-- 内容区域 -->
      <div class="layout-content">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </div>
    </div>
    
    <!-- 移动端遮罩 -->
    <div
      v-if="appStore.device === 'mobile' && !appStore.sidebarCollapsed"
      class="mobile-mask"
      @click="appStore.setSidebarCollapsed(true)"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import SidebarItem from './components/SidebarItem.vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const appStore = useAppStore()

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 用户头像
const userAvatar = computed(() => '')

// 菜单路由
const menuRoutes = computed(() => {
  const routes = router.getRoutes()
  return routes.filter(route => {
    // 过滤掉隐藏的路由和没有子路由的路由
    if (route.meta?.hidden || !route.children?.length) {
      return false
    }
    
    // 检查权限
    if (route.meta?.roles && route.meta.roles.length > 0) {
      return route.meta.roles.includes(userStore.userInfo?.role || '')
    }
    
    return true
  })
})

// 面包屑
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  const breadcrumbs = matched.map(item => ({
    title: item.meta?.title as string,
    path: item.path
  }))
  
  return breadcrumbs
})

// 处理下拉菜单命令
const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/profile/settings')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        await userStore.logout()
        ElMessage.success('退出登录成功')
        router.push('/login')
      } catch (error) {
        // 用户取消
      }
      break
  }
}

// 监听路由变化，移动端自动收起侧边栏
watch(
  () => route.path,
  () => {
    if (appStore.device === 'mobile') {
      appStore.setSidebarCollapsed(true)
    }
  }
)
</script>

<style scoped>
.layout {
  display: flex;
  height: 100vh;
}

.layout-sidebar {
  width: 200px;
  background: #001529;
  transition: width 0.3s;
  position: relative;
  z-index: 100;
}

.layout-sidebar.collapsed {
  width: 64px;
}

.sidebar-logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  border-bottom: 1px solid #1f1f1f;
}

.logo-img {
  width: 32px;
  height: 32px;
}

.logo-text {
  margin-left: 12px;
  color: #fff;
  font-size: 18px;
  font-weight: 600;
}

.sidebar-menu {
  border-right: none;
  height: calc(100vh - 60px);
  overflow-y: auto;
}

.layout-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.layout-header {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.breadcrumb {
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.user-info:hover {
  background-color: #f5f5f5;
}

.user-name {
  font-size: 14px;
  color: #333;
}

.dropdown-icon {
  font-size: 12px;
  color: #999;
}

.layout-content {
  flex: 1;
  padding: 20px;
  overflow: auto;
  background: #f5f5f5;
}

.mobile-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.3);
  z-index: 99;
}

/* 移动端样式 */
@media (max-width: 768px) {
  .layout-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 100;
    transform: translateX(-100%);
    transition: transform 0.3s;
  }
  
  .layout-sidebar.show {
    transform: translateX(0);
  }
  
  .layout-main {
    margin-left: 0;
  }
  
  .layout-content {
    padding: 12px;
  }
  
  .header-left {
    gap: 8px;
  }
  
  .breadcrumb {
    display: none;
  }
  
  .user-name {
    display: none;
  }
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
