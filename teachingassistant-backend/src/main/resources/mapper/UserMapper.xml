<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teachingassistant.mapper.UserMapper">

    <!-- 结果映射 -->
    <resultMap id="UserResultMap" type="com.teachingassistant.entity.User">
        <id property="userId" column="user_id"/>
        <result property="schoolId" column="school_id"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
        <result property="role" column="role"/>
        <result property="realName" column="real_name"/>
        <result property="phone" column="phone"/>
        <result property="mfaSecret" column="mfa_secret"/>
        <result property="status" column="status"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <association property="school" javaType="com.teachingassistant.entity.School">
            <id property="schoolId" column="s_school_id"/>
            <result property="name" column="s_name"/>
            <result property="address" column="s_address"/>
            <result property="adminQuota" column="s_admin_quota"/>
            <result property="status" column="s_status"/>
        </association>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="BaseColumns">
        u.user_id, u.school_id, u.username, u.password, u.role, u.real_name, 
        u.phone, u.mfa_secret, u.status, u.created_at, u.updated_at
    </sql>

    <!-- 关联学校查询字段 -->
    <sql id="UserWithSchoolColumns">
        <include refid="BaseColumns"/>,
        s.school_id as s_school_id, s.name as s_name, s.address as s_address, 
        s.admin_quota as s_admin_quota, s.status as s_status
    </sql>

    <!-- 根据用户名查询用户 -->
    <select id="findByUsername" resultMap="UserResultMap">
        SELECT <include refid="UserWithSchoolColumns"/>
        FROM users u
        LEFT JOIN schools s ON u.school_id = s.school_id
        WHERE u.username = #{username}
    </select>

    <!-- 根据用户ID查询用户 -->
    <select id="findById" resultMap="UserResultMap">
        SELECT <include refid="UserWithSchoolColumns"/>
        FROM users u
        LEFT JOIN schools s ON u.school_id = s.school_id
        WHERE u.user_id = #{userId}
    </select>

    <!-- 根据学校ID查询用户列表 -->
    <select id="findBySchoolId" resultMap="UserResultMap">
        SELECT <include refid="BaseColumns"/>
        FROM users u
        WHERE u.school_id = #{schoolId}
        ORDER BY u.created_at DESC
    </select>

    <!-- 根据角色查询用户列表 -->
    <select id="findByRole" resultMap="UserResultMap">
        SELECT <include refid="UserWithSchoolColumns"/>
        FROM users u
        LEFT JOIN schools s ON u.school_id = s.school_id
        WHERE u.role = #{role}
        ORDER BY u.created_at DESC
    </select>

    <!-- 根据学校ID和角色查询用户列表 -->
    <select id="findBySchoolIdAndRole" resultMap="UserResultMap">
        SELECT <include refid="BaseColumns"/>
        FROM users u
        WHERE u.school_id = #{schoolId} AND u.role = #{role}
        ORDER BY u.created_at DESC
    </select>

    <!-- 分页查询用户列表 -->
    <select id="findWithPagination" resultMap="UserResultMap">
        SELECT <include refid="UserWithSchoolColumns"/>
        FROM users u
        LEFT JOIN schools s ON u.school_id = s.school_id
        <where>
            <if test="schoolId != null">
                AND u.school_id = #{schoolId}
            </if>
            <if test="role != null and role != ''">
                AND u.role = #{role}
            </if>
            <if test="realName != null and realName != ''">
                AND u.real_name LIKE CONCAT('%', #{realName}, '%')
            </if>
        </where>
        ORDER BY u.created_at DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计用户数量 -->
    <select id="countUsers" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM users u
        <where>
            <if test="schoolId != null">
                AND u.school_id = #{schoolId}
            </if>
            <if test="role != null and role != ''">
                AND u.role = #{role}
            </if>
            <if test="realName != null and realName != ''">
                AND u.real_name LIKE CONCAT('%', #{realName}, '%')
            </if>
        </where>
    </select>

    <!-- 插入用户 -->
    <insert id="insert" parameterType="com.teachingassistant.entity.User" useGeneratedKeys="true" keyProperty="userId">
        INSERT INTO users (school_id, username, password, role, real_name, phone, mfa_secret, status)
        VALUES (#{schoolId}, #{username}, #{password}, #{role}, #{realName}, #{phone}, #{mfaSecret}, #{status})
    </insert>

    <!-- 更新用户信息 -->
    <update id="update" parameterType="com.teachingassistant.entity.User">
        UPDATE users
        <set>
            <if test="schoolId != null">school_id = #{schoolId},</if>
            <if test="realName != null and realName != ''">real_name = #{realName},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="mfaSecret != null">mfa_secret = #{mfaSecret},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            updated_at = CURRENT_TIMESTAMP
        </set>
        WHERE user_id = #{userId}
    </update>

    <!-- 更新用户密码 -->
    <update id="updatePassword">
        UPDATE users 
        SET password = #{password}, updated_at = CURRENT_TIMESTAMP
        WHERE user_id = #{userId}
    </update>

    <!-- 更新用户状态 -->
    <update id="updateStatus">
        UPDATE users 
        SET status = #{status}, updated_at = CURRENT_TIMESTAMP
        WHERE user_id = #{userId}
    </update>

    <!-- 删除用户 -->
    <delete id="deleteById">
        DELETE FROM users WHERE user_id = #{userId}
    </delete>

    <!-- 检查用户名是否存在 -->
    <select id="existsByUsername" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0 FROM users WHERE username = #{username}
    </select>

    <!-- 检查手机号是否存在 -->
    <select id="existsByPhone" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0 FROM users WHERE phone = #{phone}
    </select>

</mapper>
